<?php

/**
 * SDK监控服务类
 * @desc SDK监控服务类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/04/25
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Services;

use App\Services\PerfMate\GetMonitorData as GetPerfMateMonitorData;
use App\Services\HitBug\GetMonitorData as GetHitBugMonitorData;
use App\Services\Cls\GetMonitorData as GetClsMonitorData;
use App\Services\Cache\SdkMonitorCacheService;

class SdkMonitorService
{
    /**
     * @var int 开发者应用ID
     */
    private $developerAppId;

    /**
     * @var string 开始时间
     */
    private $startTime;

    /**
     * @var string 结束时间
     */
    private $endTime;

    /**
     * @var SdkMonitorCacheService 缓存服务
     */
    private $cacheService;

    /**
     * @var int|null 系统类型（可选）
     */
    private $osType;

    /**
     * 构造函数，初始化共用参数
     *
     * @param int $developerAppId 开发者应用ID
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @param int|null $osType 系统类型（可选）
     */
    public function __construct($developerAppId, $startTime, $endTime, $osType = null)
    {
        if (empty($developerAppId)) {
            throw new \InvalidArgumentException('开发者应用ID不能为空');
        }

        $this->developerAppId = $developerAppId;
        $this->startTime = $startTime;
        $this->endTime = $endTime;
        $this->osType = $osType;
        $this->cacheService = new SdkMonitorCacheService();
    }

    /**
     * 获取腾讯云CLS日志数据
     *
     * @return array 以event_name为键的日志分析记录
     * @throws \Exception 如果查询失败
     */
    public function getClsData()
    {
        $clsMonitorData = new GetClsMonitorData($this->startTime, $this->endTime, $this->developerAppId, $this->osType);
        return [
            'sdk_data' => $clsMonitorData->getData(),
            'inner_version_data' => $clsMonitorData->getAppLaunchData(),
            'app_version_data' => $clsMonitorData->getAppLaunchDataByAppVersion(),
        ];
    }

    /**
     * 获取SDK异常数据
     *
     * @param array $innerVersion 内部版本号数组
     * @param array $appVersion 应用版本号数组
     * @return array 包含两种类型异常数据的数组
     * @throws \Exception 如果请求失败
     */
    public function getHitBugData($innerVersion = [], $appVersion = [])
    {
        $hitBugMonitorData = new GetHitBugMonitorData($this->startTime, $this->endTime, $this->developerAppId, $innerVersion, $this->osType);
        return [
            'hitbug_data' => $hitBugMonitorData->getData(),
            'inner_version_data' => $hitBugMonitorData->getChartDataByInnerVersion($innerVersion),
            'app_version_data' => $hitBugMonitorData->getChartDataByAppVersion($appVersion),
        ];
    }

    /**
     * 获取性能监控数据
     *
     * @param array $innerVersion 内部版本号数组
     * @param array $appVersion 应用版本号数组
     * @return array 处理后的性能监控数据
     * @throws \Exception 如果请求失败
     */
    public function getPerfMateData($innerVersion = [], $appVersion = [])
    {
        $perfMateMonitorData = new GetPerfMateMonitorData($this->startTime, $this->endTime, $this->developerAppId, $innerVersion, $this->osType);
        return [
            'perfmate_data' => $perfMateMonitorData->getData(),
            'inner_version_data' => $perfMateMonitorData->getChartDataByInnerVersion($innerVersion),
            'app_version_data' => $perfMateMonitorData->getChartDataByGameVersion($appVersion),
        ];
    }

    /**
     * 获取完整的仪表板数据
     *
     * 整合CLS、HitBug和PerfMate的监控数据，生成完整的仪表板数据
     * 支持缓存机制，优先从缓存获取数据，缓存未命中时查询数据库并缓存结果
     *
     * @return array 包含所有监控数据的完整仪表板数据
     * @throws \Exception 如果数据获取失败
     */
    public function getDashboardData(): array
    {
        // 尝试从缓存获取数据
        $cachedData = $this->cacheService->getCachedData(
            $this->developerAppId,
            $this->startTime,
            $this->endTime,
            $this->osType
        );

        if ($cachedData !== null) {
            return $cachedData;
        }

        // 缓存未命中，查询数据库
        $dashboardData = $this->fetchDashboardDataFromDatabase();

        // 将数据存储到缓存
        $this->cacheService->setCachedData(
            $this->developerAppId,
            $this->startTime,
            $this->endTime,
            $dashboardData,
            $this->osType
        );

        return $dashboardData;
    }

    /**
     * 从数据库获取仪表板数据
     *
     * 原有的数据获取逻辑，从各个数据源获取并整合数据
     * 提供给定时任务等外部调用使用
     *
     * @return array 包含所有监控数据的完整仪表板数据
     * @throws \Exception 如果数据获取失败
     */
    public function fetchDashboardDataFromDatabase(): array
    {
        // 获取基础监控数据
        $clsData = $this->getClsData();
        $versionData = $this->extractVersionData($clsData);

        // 获取异常和性能监控数据
        $hitBugData = $this->getHitBugData($versionData['inner_versions'], $versionData['app_versions']);
        $perfMateData = $this->getPerfMateData($versionData['inner_versions'], $versionData['app_versions']);

        // 构建完整的仪表板数据
        return $this->buildDashboardResult($clsData, $hitBugData, $perfMateData);
    }

    /**
     * 提取版本数据
     *
     * 从CLS数据中提取内部版本号和应用版本号列表
     *
     * @param array $clsData CLS监控数据
     * @return array 包含inner_versions和app_versions的数组
     */
    private function extractVersionData(array $clsData): array
    {
        return [
            'inner_versions' => array_column($clsData['inner_version_data'], 'inner_version'),
            'app_versions' => array_column($clsData['app_version_data'], 'app_version'),
        ];
    }

    /**
     * 构建仪表板结果数据
     *
     * 整合各种监控数据，构建完整的仪表板响应数据
     *
     * @param array $clsData CLS监控数据
     * @param array $hitBugData 异常监控数据
     * @param array $perfMateData 性能监控数据
     * @return array 完整的仪表板数据
     */
    private function buildDashboardResult(array $clsData, array $hitBugData, array $perfMateData): array
    {
        return [
            'inner_version' => $this->buildVersionResult($clsData['inner_version_data'], $hitBugData, $perfMateData, 'inner_version'),
            'app_version' => $this->buildVersionResult($clsData['app_version_data'], $hitBugData, $perfMateData, 'app_version'),
            'hit_bug_data' => $hitBugData['hitbug_data'],
            'perf_mate_data' => $perfMateData['perfmate_data'],
            'sdk_data' => $clsData['sdk_data'],
            'sdk_rate' => $this->calculateSdkRates($clsData['sdk_data']),
        ];
    }

    /**
     * 构建版本结果数据
     *
     * 为指定版本类型构建包含错误率、崩溃率和流畅度的数据
     *
     * @param array $versionData 版本基础数据
     * @param array $hitBugData 异常监控数据
     * @param array $perfMateData 性能监控数据
     * @param string $versionType 版本类型（inner_version 或 app_version）
     * @return array 版本结果数据数组
     */
    private function buildVersionResult(array $versionData, array $hitBugData, array $perfMateData, string $versionType): array
    {
        $result = [];

        foreach ($versionData as $item) {
            $versionKey = $item[$versionType];

            $result[] = [
                $versionType => $versionKey,
                'app_launch_error_rate' => $item['app_launch_error_rate'],
                'crash_rate' => $hitBugData["{$versionType}_data"][$versionKey]['crash_rate'] ?? 0,
                'error_rate' => $hitBugData["{$versionType}_data"][$versionKey]['error_rate'] ?? 0,
                'smoothness' => $perfMateData["{$versionType}_data"][$versionKey]['smoothness'] ?? 0,
            ];
        }

        return $result;
    }

    /**
     * 计算SDK各项比率指标
     *
     * 基于SDK原始数据计算各种错误率、平均时间等关键指标
     *
     * @param array $sdkRawData SDK原始统计数据
     * @return array 包含各项比率指标的数组
     */
    private function calculateSdkRates(array $sdkRawData): array
    {
        // 获取基准计数，用于计算比率
        $baseCount = $sdkRawData['app_launch_screen_completion']['count'] ?? 1;

        return [
            'average_startup_time' => $this->formatDecimal($sdkRawData['app_launch_screen_completion']['avg_value'] ?? 0),
            'app_launch_error_rate' => $this->calculateErrorRate($sdkRawData['app_launch_error']['count'] ?? 0, $baseCount),
            'hot_update_time' => $this->calculateAverageTime($sdkRawData['hot_update_time_exception']['sum_value'] ?? 0, $sdkRawData['hot_update_time_exception']['count'] ?? 1),
            'hot_update_error_rate' => $this->calculateErrorRate($sdkRawData['hot_update_error']['count'] ?? 0, $baseCount),
            'sdk_init_error_rate' => $this->calculateSuccessFailureRate($sdkRawData['sdk_init_error']['count'] ?? 0, $sdkRawData['sdk_init_success']['count'] ?? 0),
            'sdk_login_error_rate' => $this->calculateSuccessFailureRate($sdkRawData['sdk_login_error']['count'] ?? 0, $sdkRawData['sdk_login_success']['count'] ?? 0),
            'default_server_request_error_rate' => $this->calculateErrorRate($sdkRawData['default_server_request_error']['count'] ?? 0, $baseCount),
            'server_request_error_rate' => $this->calculateErrorRate($sdkRawData['server_request_error']['count'] ?? 0, $baseCount),
            'login_game_error_rate' => $this->calculateErrorRate($sdkRawData['login_game_error']['count'] ?? 0, $baseCount),
            'come_in_game_error_rate' => $this->calculateErrorRate($sdkRawData['come_in_game_error']['count'] ?? 0, $baseCount),
            'login_to_main_screen_time' => $this->calculateAverageTime($sdkRawData['login_to_main_screen_time']['sum_value'] ?? 0, $sdkRawData['login_to_main_screen_time']['count'] ?? 1),
            'download_error_rate' => $this->calculateErrorRate($sdkRawData['download_error']['count'] ?? 0, $baseCount),
            'ui_open_time' => $this->calculateAverageTime($sdkRawData['ui_open_time']['sum_value'] ?? 0, $sdkRawData['ui_open_time']['count'] ?? 1),
            'sence_change_time' => $this->calculateAverageTime($sdkRawData['sence_change_time']['sum_value'] ?? 0, $sdkRawData['sence_change_time']['count'] ?? 1),
            'network_disconnection' => $this->calculateRatio($sdkRawData['network_disconnection']['count'] ?? 0, $baseCount),
            // 新增字段
            'order_failure_rate' => $this->calculateSuccessFailureRate($sdkRawData['sdk_get_order_error']['count'] ?? 0, $sdkRawData['sdk_get_order_success']['count'] ?? 0),
            'pay_call_duration' => $this->calculateAverageTime($sdkRawData['sdk_pay_page_time']['sum_value'] ?? 0, $sdkRawData['sdk_pay_page_time']['count'] ?? 1),
            'black_product_interception_rate' => $this->calculateBlackProductInterceptionRate($sdkRawData['sdk_pay_black']['count'] ?? 0, $sdkRawData['sdk_pay_times']['count'] ?? 1),
            'payment_failure_rate' => $this->calculateSuccessFailureRate($sdkRawData['sdk_pay_fail']['count'] ?? 0, $sdkRawData['sdk_pay_success']['count'] ?? 0),
            'account_delay_rate' => $this->calculateRatio($sdkRawData['sdk_pay_complete_delay']['sdk_pay_complete_delay_count'] ?? 0, $sdkRawData['sdk_pay_complete_delay']['count'] ?? 1),
            'recharge_delivery_failure_rate' => $this->calculateSuccessFailureRate($sdkRawData['sdk_pay_complete_error']['count'] ?? 0, $sdkRawData['sdk_pay_complete_success']['count'] ?? 0),
        ];
    }

    /**
     * 计算错误率
     *
     * 计算错误次数相对于总次数的百分比
     *
     * @param int $errorCount 错误次数
     * @param int $totalCount 总次数
     * @return string 格式化后的错误率百分比字符串
     */
    private function calculateErrorRate(int $errorCount, int $totalCount): string
    {
        $rate = ($errorCount / max($totalCount, 1)) * 100;
        return $this->formatDecimal($rate);
    }

    /**
     * 计算成功失败比率
     *
     * 计算失败次数相对于总操作次数（成功+失败）的百分比
     *
     * @param int $failureCount 失败次数
     * @param int $successCount 成功次数
     * @return string 格式化后的失败率百分比字符串
     */
    private function calculateSuccessFailureRate(int $failureCount, int $successCount): string
    {
        $totalCount = max($failureCount + $successCount, 1);
        $rate = ($failureCount / $totalCount) * 100;
        return $this->formatDecimal($rate);
    }

    /**
     * 计算平均时间
     *
     * 计算总时间除以次数得到的平均时间
     *
     * @param float $totalTime 总时间
     * @param int $count 次数
     * @return string 格式化后的平均时间字符串
     */
    private function calculateAverageTime(float $totalTime, int $count): string
    {
        $averageTime = $totalTime / max($count, 1);
        return $this->formatDecimal($averageTime);
    }

    /**
     * 计算比率
     *
     * 计算两个数值的比率
     *
     * @param float $numerator 分子
     * @param float $denominator 分母
     * @return string 格式化后的比率字符串
     */
    private function calculateRatio(float $numerator, float $denominator): string
    {
        $ratio = $numerator / max($denominator, 1);
        return $this->formatDecimal($ratio);
    }

    /**
     * 计算黑产拦截率
     *
     * 计算黑产拦截次数相对于支付次数的百分比
     *
     * @param int $blackCount 黑产拦截次数
     * @param int $payTimesCount 支付次数
     * @return string 格式化后的黑产拦截率百分比字符串
     */
    private function calculateBlackProductInterceptionRate(int $blackCount, int $payTimesCount): string
    {
        $rate = ($blackCount / max($payTimesCount, 1)) * 100;
        return $this->formatDecimal($rate);
    }

    /**
     * 格式化小数
     *
     * 将数值格式化为保留两位小数的字符串
     *
     * @param float $value 要格式化的数值
     * @return string 格式化后的字符串，保留两位小数
     */
    private function formatDecimal(float $value): string
    {
        return bcadd(0, round($value, 2), 2);
    }
}
