<?php

/**
 * SDK监控仪表板资源类
 * @desc 格式化SDK监控仪表板数据的响应结构，提供统一的数据格式
 * <AUTHOR> chen<PERSON><PERSON><EMAIL>
 * @date 2025-05-29
 */

namespace App\Http\Resources\SdkMonitor;

use App\Http\Resources\Base\BaseJsonResource;

class DashboardResource extends BaseJsonResource
{
    /**
     * 将资源转换为数组
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray($request)
    {
        return [
            'inner_version' => $this->formatVersionData($this->resource['inner_version'] ?? []),    // 资源版本数据
            'app_version' => $this->formatVersionData($this->resource['app_version'] ?? []),    // 应用版本数据
            'hit_bug_data' => $this->formatHitBugData($this->resource['hit_bug_data'] ?? []),    // hitbug数据
            'perf_mate_data' => $this->formatPerfMateData($this->resource['perf_mate_data'] ?? []),    // perfmate数据
            'sdk_data' => $this->formatSdkData($this->resource['sdk_data'] ?? []),    // sdk监控数据
            'sdk_rate' => $this->formatSdkRate($this->resource['sdk_rate'] ?? [])    // sdk监控比率数据
        ];
    }

    /**
     * 格式化版本数据
     *
     * @param array $versionData
     * @return array
     */
    protected function formatVersionData(array $versionData): array
    {
        return array_map(function ($item) {
            return [
                'version' => (string) ($item['inner_version'] ?? $item['app_version'] ?? ''),    // 版本
                'app_launch_error_rate' => (string) ($item['app_launch_error_rate'] ?? 0),    // 启动错误率
                'crash_rate' => (string) ($item['crash_rate'] ?? 0),    // 崩溃率
                'error_rate' => (string) ($item['error_rate'] ?? 0),    // 错误率
                'smoothness' => (string) ($item['smoothness'] ?? 0)    // 卡顿率
            ];
        }, $versionData);
    }

    /**
     * 格式化HitBug数据
     *
     * @param array $hitBugData
     * @return array
     */
    protected function formatHitBugData(array $hitBugData): array
    {
        return [
            'crash' => (string) ($hitBugData['crash'] ?? 0),    // 崩溃率
            'error' => (string) ($hitBugData['error'] ?? 0),    // 错误率
            'start_count' => (string) ($hitBugData['start_count'] ?? 0),    // 启动数
            'crash_count' => (string) ($hitBugData['crash_count'] ?? 0),    // 崩溃数
            'error_count' => (string) ($hitBugData['error_count'] ?? 0),    // 错误数
            'start_dev_num' => (string) ($hitBugData['start_dev_num'] ?? 0),    // 启动设备数
            'crash_dev_num' => (string) ($hitBugData['crash_dev_num'] ?? 0),    // 崩溃设备数
            'error_dev_num' => (string) ($hitBugData['error_dev_num'] ?? 0)    // 错误设备数
        ];
    }

    /**
     * 格式化PerfMate数据
     *
     * @param array $perfMateData
     * @return array
     */
    protected function formatPerfMateData(array $perfMateData): array
    {
        return [
            'smoothness' => (string) ($perfMateData['smoothness'] ?? 0),    // 卡顿率
            'avg_memory' => (string) ($perfMateData['avg_memory'] ?? 0),    // 平均内存
            'avg_network_traffic' => (string) ($perfMateData['avg_network_traffic'] ?? 0),    // 平均网络流量
            'avg_network_delay' => (string) ($perfMateData['avg_network_delay'] ?? 0),    // 平均网络延迟
            'avg_battery_power' => (string) ($perfMateData['avg_battery_power'] ?? 0),    // 平均电池电量
            'avg_battery_temp' => (string) ($perfMateData['avg_battery_temp'] ?? 0),    // 平均电池温度
            'avg_fps_power' => (string) ($perfMateData['avg_fps_power'] ?? 0)    // 每帧功耗
        ];
    }

    /**
     * 格式化SDK比率数据
     *
     * @param array $sdkRate
     * @return array
     */
    protected function formatSdkRate(array $sdkRate): array
    {
        return [
            'average_startup_time' => (string) ($sdkRate['average_startup_time'] ?? 0),    // 平均启动时间
            'app_launch_error_rate' => (string) ($sdkRate['app_launch_error_rate'] ?? 0),    // 启动错误率
            'hot_update_time' => (string) ($sdkRate['hot_update_time'] ?? 0),    // 热更新时间
            'hot_update_error_rate' => (string) ($sdkRate['hot_update_error_rate'] ?? 0),    // 热更新错误率
            'sdk_init_error_rate' => (string) ($sdkRate['sdk_init_error_rate'] ?? 0),    // sdk初始化错误率
            'sdk_login_error_rate' => (string) ($sdkRate['sdk_login_error_rate'] ?? 0),    // sdk登录错误率
            'default_server_request_error_rate' => (string) ($sdkRate['default_server_request_error_rate'] ?? 0),    // 默认服务器请求错误率
            'server_request_error_rate' => (string) ($sdkRate['server_request_error_rate'] ?? 0),    // 服务器请求错误率
            'login_game_error_rate' => (string) ($sdkRate['login_game_error_rate'] ?? 0),    // 登录游戏错误率
            'come_in_game_error_rate' => (string) ($sdkRate['come_in_game_error_rate'] ?? 0),    // 进入游戏错误率
            'login_to_main_screen_time' => (string) ($sdkRate['login_to_main_screen_time'] ?? 0),    // 登录到主屏幕时间
            'download_error_rate' => (string) ($sdkRate['download_error_rate'] ?? 0),    // 下载错误率
            'ui_open_time' => (string) ($sdkRate['ui_open_time'] ?? 0),    // ui打开时间
            'scene_change_time' => (string) ($sdkRate['sence_change_time'] ?? 0),    // 场景切换时间
            'network_disconnection' => (string) ($sdkRate['network_disconnection'] ?? 0),    // 网络断开次数
            // 新增字段
            'order_failure_rate' => (string) ($sdkRate['order_failure_rate'] ?? 0),    // 订单失败率
            'pay_call_duration' => (string) ($sdkRate['pay_call_duration'] ?? 0),    // 支付调用耗时
            'black_product_interception_rate' => (string) ($sdkRate['black_product_interception_rate'] ?? 0),    // 黑产拦截率
            'payment_failure_rate' => (string) ($sdkRate['payment_failure_rate'] ?? 0),    // 支付失败率
            'account_delay_rate' => (string) ($sdkRate['account_delay_rate'] ?? 0),    // 到账延率
            'recharge_delivery_failure_rate' => (string) ($sdkRate['recharge_delivery_failure_rate'] ?? 0)    // 充值发货失败率
        ];
    }

    /**
     * 格式化SDK数据
     *
     * @param array $sdkData
     * @return array
     */
    protected function formatSdkData(array $sdkData): array
    {
        $formattedData = [];

        foreach ($sdkData as $key => $eventData) {
            if (is_array($eventData)) {
                $formattedData[$key] = [
                    'event_name' => (string) ($eventData['event_name'] ?? ''),    // 事件名称
                    'count' => (string) ($eventData['count'] ?? 0),    // 事件数量
                    'avg_value' => number_format((float) ($eventData['avg_value'] ?? 0), 2, '.', ''),    // 平均值
                    'sum_value' => (string) ($eventData['sum_value'] ?? 0)    // 总和
                ];
            } else {
                $formattedData[$key] = $eventData;
            }
        }

        return $formattedData;
    }
}
