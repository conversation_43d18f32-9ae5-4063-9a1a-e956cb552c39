<?php

/**
 * 系统类型常量定义类
 * @desc 定义SDK监控系统中支持的操作系统类型常量和标签映射，为整个项目提供统一的系统类型引用
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2025-01-15
 * @todo 暂无特定待办事项
 */

namespace App\Constants;

class OsTypeConstants
{
    /**
     * 安卓系统
     */
    const ANDROID = 1;

    /**
     * iOS系统
     */
    const IOS = 2;

    /**
     * PC系统
     */
    const PC = 3;

    /**
     * 小程序
     */
    const MINI_PROGRAM = 4;

    /**
     * 鸿蒙系统
     */
    const HARMONY = 5;

    /**
     * 系统类型标签映射
     *
     * @var array<int, string>
     */
    const LABELS = [
        self::ANDROID => '安卓',
        self::IOS => 'iOS',
        self::PC => 'PC',
        self::MINI_PROGRAM => '小程序',
        self::HARMONY => '鸿蒙'
    ];

    /**
     * 获取系统类型的中文标签
     *
     * @param int $osType 系统类型值
     * @return string 对应的中文标签，如果类型不存在则返回'未知'
     */
    public static function getLabel(int $osType): string
    {
        return self::LABELS[$osType] ?? '未知';
    }

    /**
     * 获取所有有效的系统类型值
     *
     * @return array<int> 系统类型值数组
     */
    public static function getValidTypes(): array
    {
        return array_keys(self::LABELS);
    }

    /**
     * 检查给定的系统类型值是否有效
     *
     * @param int $osType 系统类型值
     * @return bool 如果类型有效返回true，否则返回false
     */
    public static function isValidType(int $osType): bool
    {
        return array_key_exists($osType, self::LABELS);
    }
}
