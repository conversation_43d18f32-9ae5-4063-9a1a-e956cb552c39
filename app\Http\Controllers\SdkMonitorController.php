<?php

/**
 * SDK监控服务控制器
 * @desc 处理SDK监控相关的API请求，提供仪表板数据展示功能
 * <AUTHOR> <EMAIL>
 * @date 2025-05-29
 */

namespace App\Http\Controllers;

use App\Http\Requests\SdkMonitor\DashboardRequest;
use App\Http\Resources\SdkMonitor\DashboardResource;
use App\Services\SdkMonitorService;
use App\Traits\ApiResponseTrait;

class SdkMonitorController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取SDK监控仪表板数据
     *
     * 获取包含版本数据、异常数据、性能数据和SDK比率数据的综合监控信息
     *
     * @param DashboardRequest $request 经过验证的请求对象，包含开发者应用ID、时间范围和可选的系统类型
     * @return \Illuminate\Http\JsonResponse 包含仪表板数据的JSON响应
     * @throws \Exception 如果数据获取失败
     */
    public function dashboard(DashboardRequest $request)
    {
        // 获取经过验证的请求参数
        $validated = $request->validated();

        // 初始化SDK监控服务并获取仪表板数据
        $sdkMonitorService = new SdkMonitorService(
            $validated['developer_app_id'],
            $validated['start_time'],
            $validated['end_time'],
            $validated['os_type'] ?? null
        );

        // 获取完整的仪表板数据
        $dashboardData = $sdkMonitorService->getDashboardData();

        return $this->respondSuccess(
            new DashboardResource($dashboardData),
            trans('response.success')
        );
    }
}
