# 大盘模块 API 设计文档 (Laravel 10)

> 作者：陈建权
>
> 日期：2025-06-05
>
> 版本：1.0.0

本文档详细描述基于 Laravel 10 的大盘功能模块的 API 接口设计。该系统提供 SDK 监控数据的综合展示功能，整合版本数据、异常数据、性能数据和 SDK 比率数据，为开发者提供全面的监控大盘。

## 核心要素概览

1. **控制器**: 处理大盘数据请求的核心业务逻辑，提供统一的数据获取接口
2. **服务类**: SDK 监控服务，负责整合多个数据源的监控数据
3. **API 路由**: 模块提供的大盘数据获取端点
4. **表单请求**: API 请求数据的验证规则，确保参数的有效性和安全性
5. **API 资源**: API 响应数据的格式化和转换，提供统一的数据结构
6. **缓存机制**: 支持数据缓存，提高响应性能和减少数据库查询压力
7. **响应格式**: 统一的 API 响应格式，包含 code、message 和 data 字段
8. **数据整合**: 整合 CLS、HitBug 和 PerfMate 三个监控系统的数据

---

## 1. 控制器

### 1.1 SdkMonitorController

* **类名:** `App\Http\Controllers\SdkMonitorController`
* **继承自:** `Controller`
* **使用的 Traits:**
  * `App\Traits\ApiResponseTrait`: 用于提供统一的 API 响应方法
* **主要职责:** 处理 SDK 监控相关的 API 请求，提供大盘数据展示功能

#### dashboard(DashboardRequest $request)

获取 SDK 监控大盘数据的核心方法。

**实现逻辑:**
1. 获取经过验证的请求参数（开发者应用ID、开始时间、结束时间、系统类型）
2. 初始化 `SdkMonitorService` 服务实例
3. 调用服务的 `getDashboardData()` 方法获取完整的大盘数据
4. 使用 `DashboardResource` 格式化响应数据
5. 返回包含大盘数据的统一格式 JSON 响应

**响应示例:**
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "inner_version": [
      {
        "version": "202410261816",
        "app_launch_error_rate": "0.00",
        "crash_rate": "0",
        "error_rate": "0",
        "smoothness": "0"
      }
    ],
    "app_version": [
      {
        "version": "1.1.1",
        "app_launch_error_rate": "0.00",
        "crash_rate": "0",
        "error_rate": "0",
        "smoothness": "0"
      }
    ],
    "hit_bug_data": {
      "crash": "0.00",
      "error": "0.00",
      "start_count": "0",
      "crash_count": "0",
      "error_count": "0"
    },
    "perf_mate_data": {
      "smoothness": "0",
      "avg_memory": "0",
      "avg_network_traffic": "0",
      "avg_network_delay": "0"
    },
    "sdk_data": {
      "app_launch_error": {
        "event_name": "app_launch_error",
        "count": "6",
        "avg_value": "0.00",
        "sum_value": "0"
      }
    },
    "sdk_rate": {
      "average_startup_time": "0.00",
      "app_launch_error_rate": "100.00",
      "hot_update_time": "3.00",
      "hot_update_error_rate": "15.50",
      "sdk_init_error_rate": "8.75",
      "sdk_login_error_rate": "12.30",
      "default_server_request_error_rate": "5.20",
      "server_request_error_rate": "6.80",
      "login_game_error_rate": "4.50",
      "come_in_game_error_rate": "3.20",
      "login_to_main_screen_time": "2.50",
      "download_error_rate": "7.60",
      "ui_open_time": "1.80",
      "scene_change_time": "2.20",
      "network_disconnection": "0.50",
      "order_failure_rate": "15.50",
      "pay_call_duration": "2.30",
      "black_product_interception_rate": "5.20",
      "payment_failure_rate": "8.75",
      "account_delay_rate": "12.40",
      "recharge_delivery_failure_rate": "3.60"
    }
  }
}
```

---

## 2. 服务类

### 2.1 SdkMonitorService

* **类名:** `App\Services\SdkMonitorService`
* **主要职责:** SDK 监控服务类，负责整合多个数据源的监控数据
* **核心属性:**
  * `$developerAppId` (`int`): 开发者应用ID
  * `$startTime` (`string`): 查询开始时间
  * `$endTime` (`string`): 查询结束时间
  * `$osType` (`int|null`): 系统类型（可选）
  * `$cacheService` (`SdkMonitorCacheService`): 缓存服务实例

#### 核心方法

**getDashboardData(): array**
- 获取完整的大盘数据
- 支持缓存机制，优先从缓存获取数据
- 缓存未命中时查询数据库并缓存结果

**fetchDashboardDataFromDatabase(): array**
- 从数据库获取大盘数据
- 整合 CLS、HitBug 和 PerfMate 的监控数据
- 提供给定时任务等外部调用使用

**getClsData(): array**
- 获取腾讯云 CLS 日志数据
- 返回 SDK 数据和版本数据

**getHitBugData(array $innerVersion, array $appVersion): array**
- 获取 SDK 异常数据
- 支持按内部版本和应用版本筛选

**getPerfMateData(array $innerVersion, array $appVersion): array**
- 获取 SDK 性能数据
- 支持按内部版本和应用版本筛选

### 2.2 SdkMonitorCacheService

* **类名:** `App\Services\Cache\SdkMonitorCacheService`
* **主要职责:** 提供 SDK 监控数据的缓存功能
* **缓存配置:**
  * 缓存键前缀: `sdk_monitor`
  * 缓存时间: 10 分钟
  * 缓存键格式: `sdk_monitor:{developerAppId}:{osTypeStr}:{hash}`

#### 核心方法

**getCachedData(int $developerAppId, string $startTime, string $endTime): ?array**
- 根据参数获取缓存的大盘数据
- 返回缓存数据或 null（如果不存在）

**setCachedData(int $developerAppId, string $startTime, string $endTime, array $data): bool**
- 将大盘数据存储到缓存中
- 返回是否存储成功

**warmupCache(int $developerAppId, string $startTime, string $endTime, callable $dataProvider): bool**
- 预热缓存数据
- 支持通过回调函数获取数据

---

## 3. API 路由

* **定义文件:** `routes/api.php`
* **路由前缀:** 所有路由自动带有 `/api` 前缀
* **路由命名空间:** `App\Http\Controllers`

### 3.1 公开路由 (无需认证)

| 方法   | URI          | 控制器方法                      | 描述                                                                                                |
| :----- | :----------- | :------------------------------ | :-------------------------------------------------------------------------------------------------- |
| `GET`  | `/dashboard` | `SdkMonitorController@dashboard` | 获取 SDK 监控大盘数据，包含版本数据、异常数据、性能数据和 SDK 比率数据                            |

#### 路由注册代码

```php
// 大盘数据
Route::get('/dashboard', [SdkMonitorController::class, 'dashboard']);
```

---

## 4. 表单请求 (Form Requests)

### 4.1 `DashboardRequest` (用于 `GET /dashboard`)

* **类名:** `App\Http\Requests\SdkMonitor\DashboardRequest`
* **继承自:** `App\Http\Requests\Base\BaseFormRequest`
* **主要职责:** 验证 SDK 监控大盘接口的请求参数

#### authorize() 方法
- 权限校验已移至控制器层面，此处直接返回 `true`

#### rules() 方法
```php
[
    'developer_app_id' => [
        'required',
        'integer',
        'min:1'
    ],
    'start_time' => [
        'required',
        'date_format:Y-m-d H:i:s'
    ],
    'end_time' => [
        'required',
        'date_format:Y-m-d H:i:s',
        'after:start_time'
    ],
    'os_type' => [
        'nullable',
        'integer',
        'in:1,2,3,4,5'
    ]
]
```

#### attributes() 方法
提供验证错误的自定义属性名称，支持国际化翻译。

---

## 5. API 资源 (API Resources)

### 5.1 `DashboardResource` (`App\Http\Resources\SdkMonitor\DashboardResource`)

* **继承自:** `App\Http\Resources\Base\BaseJsonResource`
* **主要职责:** 格式化 SDK 监控大盘数据的响应结构

#### toArray($request) 方法结构
```php
[
    'inner_version' => $this->formatVersionData($this->resource['inner_version'] ?? []),
    'app_version' => $this->formatVersionData($this->resource['app_version'] ?? []),
    'hit_bug_data' => $this->formatHitBugData($this->resource['hit_bug_data'] ?? []),
    'perf_mate_data' => $this->formatPerfMateData($this->resource['perf_mate_data'] ?? []),
    'sdk_data' => $this->formatSdkData($this->resource['sdk_data'] ?? []),
    'sdk_rate' => $this->formatSdkRate($this->resource['sdk_rate'] ?? [])
]
```

#### 数据格式化方法
- `formatVersionData()`: 格式化版本数据
- `formatHitBugData()`: 格式化异常数据
- `formatPerfMateData()`: 格式化性能数据
- `formatSdkData()`: 格式化 SDK 事件数据
- `formatSdkRate()`: 格式化 SDK 比率数据

---

## 6. 数据结构

### 6.1 请求参数

| 参数名           | 类型    | 必填 | 默认值              | 描述                                    |
| :--------------- | :------ | :--- | :------------------ | :-------------------------------------- |
| developer_app_id | integer | 是   | 无                  | 开发者应用ID，必须大于0                 |
| start_time       | string  | 是   | 无                  | 开始时间，格式：Y-m-d H:i:s             |
| end_time         | string  | 是   | 无                  | 结束时间，格式：Y-m-d H:i:s，必须晚于开始时间 |
| os_type          | integer | 否   | 无                  | 系统类型，可选值：1(安卓)、2(iOS)、3(PC)、4(小程序)、5(鸿蒙) |

### 6.2 响应数据结构

#### inner_version / app_version 数组
- `version`: 版本号（内部版本为时间戳格式，应用版本为语义化版本）
- `app_launch_error_rate`: 应用启动错误率
- `crash_rate`: 崩溃率
- `error_rate`: 错误率
- `smoothness`: 卡顿率

#### hit_bug_data 对象
- `crash`: 崩溃率
- `error`: 错误率
- `start_count`: 启动次数
- `crash_count`: 崩溃次数
- `error_count`: 错误次数
- `start_dev_num`: 启动设备数
- `crash_dev_num`: 崩溃设备数
- `error_dev_num`: 错误设备数

#### perf_mate_data 对象
- `smoothness`: 卡顿率
- `avg_memory`: 平均内存使用（MB）
- `avg_network_traffic`: 平均网络流量（KB）
- `avg_network_delay`: 平均网络延迟（ms）
- `avg_battery_power`: 平均电池电量（%）
- `avg_battery_temp`: 平均电池温度（℃）
- `avg_fps_power`: 每帧功耗（mW）

#### sdk_data 对象
包含各种 SDK 事件的统计信息，每个事件包含：
- `event_name`: 事件名称
- `count`: 事件发生次数
- `avg_value`: 平均值
- `sum_value`: 总值

#### sdk_rate 对象
包含关键性能指标的计算结果：
- `average_startup_time`: 平均启动时间（秒）
- `app_launch_error_rate`: 应用启动错误率（%）
- `hot_update_time`: 热更新时间（秒）
- `hot_update_error_rate`: 热更新错误率（%）
- `sdk_init_error_rate`: SDK初始化错误率（%）
- `sdk_login_error_rate`: SDK登录错误率（%）
- `default_server_request_error_rate`: 默认服务器请求错误率（%）
- `server_request_error_rate`: 服务器请求错误率（%）
- `login_game_error_rate`: 登录游戏错误率（%）
- `come_in_game_error_rate`: 进入游戏错误率（%）
- `login_to_main_screen_time`: 登录到主屏幕时间（秒）
- `download_error_rate`: 下载错误率（%）
- `ui_open_time`: UI打开时间（秒）
- `scene_change_time`: 场景切换时间（秒）
- `network_disconnection`: 网络断开次数
- `order_failure_rate`: 订单失败率（%）
- `pay_call_duration`: 支付调用耗时（秒）
- `black_product_interception_rate`: 黑产拦截率（%）
- `payment_failure_rate`: 支付失败率（%）
- `account_delay_rate`: 到账延率（%）
- `recharge_delivery_failure_rate`: 充值发货失败率（%）

---

## 7. 缓存机制

### 7.1 缓存策略

* **缓存键生成:** 基于开发者应用ID、开始时间、结束时间和系统类型生成唯一的缓存键
* **缓存时间:** 10 分钟
* **缓存存储:** 使用 Laravel 默认缓存驱动
* **缓存更新:** 支持手动预热和自动过期更新
* **缓存隔离:** 不同系统类型的数据使用独立的缓存键，避免数据混淆

### 7.2 缓存流程

1. **查询缓存:** 首先尝试从缓存获取数据
2. **缓存命中:** 直接返回缓存数据，记录命中日志
3. **缓存未命中:** 查询数据库获取数据
4. **存储缓存:** 将查询结果存储到缓存中
5. **返回数据:** 返回最终的大盘数据

---

## 8. 统一响应格式

所有API响应都遵循统一的格式，包含code、message和data三个字段：

```json
{
  "code": 0,       // 业务状态码，0表示成功，其他值表示错误
  "message": "操作成功", // 业务消息，支持国际化
  "data": { ... }    // 业务数据，可能为对象、数组或null
}
```

### 8.1 成功响应

成功响应使用code=0，HTTP状态码为200：

```json
{
  "code": 0,
  "message": "操作成功",
  "data": { ... } // 大盘数据
}
```

### 8.2 错误响应

错误响应使用对应的错误码，HTTP状态码与业务状态码一致：

```json
{
  "code": 422,
  "message": "验证失败：开发者应用ID必须是整数",
  "data": null
}
```

### 8.3 常见状态码

| HTTP状态码 | 业务状态码 | 说明 |
| :--------- | :--------- | :--- |
| 200        | 0          | 请求成功 |
| 400        | 400        | 请求参数错误 |
| 422        | 422        | 请求参数验证失败 |
| 500        | 500        | 服务器内部错误 |

---

## 9. 数据源整合

### 9.1 数据源概述

大盘模块整合了三个主要的监控数据源：

1. **CLS (腾讯云日志服务)**: 提供 SDK 事件数据和版本数据
2. **HitBug**: 提供异常监控数据，包括崩溃和错误统计
3. **PerfMate**: 提供性能监控数据，包括内存、网络、电池等指标

### 9.2 数据整合流程

1. **获取基础数据:** 从 CLS 获取 SDK 事件数据和版本信息
2. **提取版本列表:** 从基础数据中提取内部版本号和应用版本号列表
3. **获取异常数据:** 使用版本列表从 HitBug 获取对应的异常监控数据
4. **获取性能数据:** 使用版本列表从 PerfMate 获取对应的性能监控数据
5. **计算比率数据:** 基于原始数据计算各种性能指标和错误率
6. **构建响应:** 将所有数据整合为统一的大盘响应格式

### 9.3 版本数据处理

* **内部版本:** 采用时间戳格式（如：202410261816）
* **应用版本:** 采用语义化版本格式（如：1.1.1）
* **版本关联:** 每个版本都包含对应的异常率、崩溃率、错误率和卡顿率数据

---

## 10. 性能优化

### 10.1 缓存优化

* **智能缓存:** 基于请求参数自动生成缓存键
* **缓存预热:** 支持定时任务预热热点数据
* **缓存监控:** 记录缓存命中率和性能指标

### 10.2 查询优化

* **批量查询:** 一次性获取所有需要的数据，减少网络请求
* **数据筛选:** 在服务层进行数据筛选和聚合，减少传输量
* **异步处理:** 支持异步数据获取和处理

### 10.3 响应优化

* **数据格式化:** 统一的数据格式化处理，确保响应一致性
* **压缩传输:** 支持 HTTP 压缩，减少传输时间
* **分页支持:** 对于大量数据支持分页处理（如需要）
